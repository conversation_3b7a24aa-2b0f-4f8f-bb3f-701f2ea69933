plugins {
    id 'java'
    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
}



dependencies {
    implementation project(':common')
    implementation project(':item')
    implementation project(':reimburse')
    implementation project(':travel-apply')

    implementation(enforcedPlatform("com.xiaohongshu:infra-root-pom:${root_pom_version}"))


    implementation('org.apache.commons:commons-lang3:3.6')
    implementation('com.alibaba:druid:1.1.3')
    implementation('ch.qos.logback:logback-classic:1.2.3')
    implementation('ch.qos.logback:logback-core:1.2.3')

    //spring
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation('mysql:mysql-connector-java:8.0.16')
    implementation('org.mybatis:mybatis:3.5.5')
    implementation('org.mybatis:mybatis-spring:2.0.5')
    implementation('com.baomidou:mybatis-plus-boot-starter:3.4.0')
    implementation('com.baomidou:mybatis-plus-extension:3.4.0')
    implementation('javax.servlet:javax.servlet-api:4.0.0')
    testImplementation 'org.springframework.boot:spring-boot-starter-test'


    //utils&middle
    implementation("com.xiaohongshu.infra.midware:redis-spring")
    implementation("com.xiaohongshu.infra.midware:mysql-spring")
    implementation('com.xiaohongshu:infra-framework-log')
    implementation('com.xiaohongshu:utils-log')
    implementation('com.xiaohongshu:gateway-starter')
    implementation("com.xiaohongshu.xray:xray-logging")
    implementation('com.xiaohongshu.fls:sso-accessor-final:1.0.2-RELEASE')
    implementation("com.xiaohongshu.fls:sso-accessor:1.1.7")

    implementation('com.xiaohongshu:thrift-springboot') { exclude group: 'com.alibaba', module: 'fastjson' }
    implementation('com.xhs.cache:cache-client:0.0.9-SNAPSHOT') { changing = true }
    implementation("com.github.jsqlparser:jsqlparser:1.1") { force = true }


    implementation('com.xhs.enterprise:erp-common:1.1.4-SNAPSHOT') {
        changing = true
        exclude group: 'com.alibaba', module: 'fastjson'
        exclude group: 'com.ctrip.framework.apollo', module: 'apollo-openapi'
        exclude group: 'com.ctrip.framework.apollo', module: 'apollo-core'
        exclude group: 'com.ctrip.framework.apollo', module: 'apollo-client'
        exclude group: 'com.xuxueli', module: 'xxl-job-core'
        exclude group: 'javax.servlet', module: 'javax.servlet-api'
    }

    implementation('com.github.pagehelper:pagehelper-spring-boot-starter:1.2.3')
    implementation(group: 'org.apache.poi', name: 'poi-ooxml', version: '3.14')
    implementation('org.assertj:assertj-core:3.11.1')
    implementation('com.xhs.enterprise:rbac-client:********-RELEASE')
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-empservice:master-20221203.033547-49')

    //消息中台
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-oamiddle:master-SNAPSHOT')

    // 短信接入
    implementation('com.xiaohongshu.sns.thrift:sms-api:master-SNAPSHOT')
    //新ehr
    implementation 'com.xiaohongshu.fls.thrift:lib-thrift-ehrservice:1.2.14'
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-ehr-public:1.0.23')
    //oacommon
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-oa-common:master-SNAPSHOT')
    //财务-出纳中心
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-cashiercenter:0.12.3')
    //财务-联行号查询
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-rftreasury:1.0.5')
    //财务-喜马拉雅
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-himalaya:1.0.10')
    //apiHub
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-apihubservice:master-SNAPSHOT')

    implementation('com.xiaohongshu:infra-redconf-client-all:2.0.0')

    implementation platform("org.springframework.boot:spring-boot-dependencies:2.2.13.RELEASE")

    implementation 'io.springfox:springfox-swagger2:2.9.2'
    implementation 'io.springfox:springfox-swagger-ui:2.9.2'

    //redFlow
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-oa-public:master-SNAPSHOT')
    // ocr
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-oa-contract:master-SNAPSHOT')
    // cdn
    implementation ('com.xiaohongshu.multicdn.thrift:multicdn-service-rpc-sdk:0.2.5')
    implementation ('com.xiaohongshu.media:media-services-sdk:1.0.3')
    // 上传文件组件sdk
    implementation('com.xiaohongshu:ros-java-sdk:0.0.18')
    implementation('com.thoughtworks.xstream:xstream:1.4.17'){ force = true }

    // kotlin
    implementation('org.jetbrains.kotlin:kotlin-stdlib:1.3.70')
    implementation('org.jetbrains.kotlin:kotlin-stdlib-common:1.3.70')

    implementation('com.xiaohongshu.fls.thrift:lib-thrift-ehrservice:1.5.9') // 人事

}

// 设置JAR包的配置
bootJar {
    enabled = true
    // 设置JAR包的文件名
    archiveFileName = "oa-office.jar"
    // 设置主类
    mainClassName = 'com.xhs.oa.office.Application'
}









