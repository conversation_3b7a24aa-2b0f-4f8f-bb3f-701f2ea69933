# 联行号查询接口 副本
接入前先评估一下qps，如果高的话请联系对于研发评估影响
maven依赖：
<dependency>
    <groupId>com.xiaohongshu.fls.thrift</groupId>
    <artifactId>lib-thrift-rftreasury</artifactId>
    <version>1.0.4</version>
</dependency>

serviceName：rftreasury-service-default
请求接口：com.xiaohongshu.finance.rpc.rftreasury.CnapsCodeQueryServiceRpc.Iface#queryCnapsCodeInfoByCodeOrBankName
请求参数：
// 查询联行号参数，两个参数至少传一个
struct CnapsCodeQueryRequest {
    // 分支联行号，精准匹配
    1: string cnapsCode,
    // 银行分支名（模糊匹配）,如果使用银行名称查询则最多只返回20条数据
    2: string bankName,
    // 来源，填你的应用名，必填
    3: string source
}

响应结果：
// 联行号查询结果
struct CnapsCodeQueryResult {
    // 是否成功
    1: bool success;
    // 状态码
    2: string code;
    // 失败消息
    3: string errorMsg;
    // 联行号信息
    4: list<CnapsCodeInfo> cnapsInfos;
}
 
struct CnapsCodeInfo{
    1: string cnapsCode, // 分行联行号
    2: string bankName, // 银行名称
    3: string superBankCode, // 总行联行号
    4: string areaCode, // 地区编码
}