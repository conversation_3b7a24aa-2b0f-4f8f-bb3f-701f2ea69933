package com.xhs.reimburse.mq.config;

import com.xhs.reimburse.mq.consumer.FinanceMessageProcessor;
import com.xhs.reimburse.mq.consumer.RedflowFormAuditProcessor;
import com.xiaohongshu.events.client.consumer.EventsPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date :2025/02/25 - 下午9:09
 * @description :
 */
@Slf4j
@Configuration
public class ConsumerConfiguration {
    // 生产者所属topic
    private static final String REDFLOW_FORM_AUDIT_TOPIC = "oasis_form-notice";
    // 消费者所属group
    private static final String REDFLOW_FORM_AUDIT_GROUP = "oasis_form-notice-oaoffice-consumer";
    // 财务消息topic
    private static final String FINANCE_MESSAGE_TOPIC = "fls-cashiercenter-master-payorder-status";
    // 财务消息消费者group
    private static final String FINANCE_MESSAGE_GROUP = "fls-cashiercenter-master-payorder-status-oa-office-consumer";

    @Bean(name = "RedflowFormAuditConsumer")
    public EventsPushConsumer RedflowFormAuditConsumer(RedflowFormAuditProcessor redflowFormAuditProcessor) {
        EventsPushConsumer consumer = new EventsPushConsumer();
        consumer.setTopic(REDFLOW_FORM_AUDIT_TOPIC);
        consumer.setGroup(REDFLOW_FORM_AUDIT_GROUP);
        consumer.setMessageProcessor(redflowFormAuditProcessor);
        consumer.start();
        log.info("------------------ RedflowFormAuditConsumer MQ启动成功！------------------");
        return consumer;
    }

    @Bean(name = "financeMessageConsumer")
    public EventsPushConsumer financeMessageConsumer(FinanceMessageProcessor financeMessageProcessor) {
        EventsPushConsumer consumer = new EventsPushConsumer();
        consumer.setTopic(FINANCE_MESSAGE_TOPIC);
        consumer.setGroup(FINANCE_MESSAGE_GROUP);
        consumer.setMessageProcessor(financeMessageProcessor);
        consumer.start();
        log.info("------------------ FinanceMessageConsumer MQ启动成功！------------------");
        return consumer;
    }
}
