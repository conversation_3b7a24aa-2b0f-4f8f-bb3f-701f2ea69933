package com.xhs.reimburse.mq.consumer;

import com.xiaohongshu.events.client.MessageExt;
import com.xiaohongshu.events.client.api.MessageProcessor;
import com.xiaohongshu.events.client.consumer.ConsumeContext;
import com.xiaohongshu.events.client.consumer.ConsumeStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class FinanceMessageProcessor implements MessageProcessor {

    @Override
    public ConsumeStatus process(MessageExt msg, ConsumeContext context) {
        log.info("FinanceMessageProcessor - 收到财务消息: {}", new String(msg.getBody()));
        
        try {
            // TODO: 实现财务消息处理逻辑
            // 消息体定义如下：
            // @Data
            //public class PaymentNotifyDto extends RocketMqPaymentResultDto{
            //    //来源类型 打款接口中的sourceType
            //    private String sourceType;
            //
            //    //来源业务类型 打款接口中的sourceBizType
            //    private String sourceBizType;
            //
            //    //来源编号 打款接口中的sourceNo
            //    private String sourceNo;
            //
            //    //小红书编号
            //    private String xhsNo;
            //
            //    //状态，枚举见PaymentNotifyStatusEnum
            //    private String status;
            //
            //    //业务状态实际发生时间
            //    private Long occurrence;
            //
            //    //错误编码
            //    private int errorCode;
            //
            //    //子错误编码（明细错误码）
            //    private String subErrorCode;
            //
            //    //错误信息
            //    private String errorMsg;
            //
            //}

            // todo
            // 支付单审批拒绝：业务单驳回到发起人，驳回原因为审批拒绝原因。
            // 支付单取消付款：不存在这种情况，业务不会主动调用取消付款。
            // 支付单付款失败：业务单驳回到发起人，驳回原因为付款失败原因。
            // 支付单付款成功：业务单据仍然为审核中。不做处理
            // 支付单据付款退票：业务单据驳回到发起人，驳回原因为退票原因。

            
        } catch (Exception e) {
            log.error("FinanceMessageProcessor - 处理财务消息异常: {}", e.getMessage(), e);
            return ConsumeStatus.SUCCESS;
        }

        return ConsumeStatus.SUCCESS;
    }
} 