package com.xhs.reimburse.service.external.finance;

import com.alibaba.fastjson2.JSON;
import com.xhs.oa.office.exception.BusinessException;
import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.oa.office.utils.RetryUtil;
import com.xhs.reimburse.modal.dto.ReimbursePaymentDto;
import com.xhs.reimburse.modal.dto.ReimbursementFormCheckResultDto;
import com.xhs.reimburse.xhsoa.mapper.FormPaymentDetailMapper;
import com.xiaohongshu.finance.rpc.rftreasury.CnapsCodeQueryRequest;
import com.xiaohongshu.finance.rpc.rftreasury.CnapsCodeQueryResult;
import com.xiaohongshu.finance.rpc.rftreasury.CnapsCodeQueryServiceRpc;
import com.xiaohongshu.fls.rpc.finance.cashiercenter.PaymentService.Iface;
import com.xiaohongshu.fls.rpc.finance.cashiercenter.model.UnifyPaymentApplyBankRequest;
import com.xiaohongshu.fls.rpc.finance.cashiercenter.model.UnifyPaymentApplyRequest;
import com.xiaohongshu.fls.rpc.finance.cashiercenter.model.UnifyPaymentApplyResponse;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Objects;

@Service
@Slf4j
public class OAPaymentService {

    // 系统来源标识
    private static final String SOURCE_TYPE = "OA-OFFICE";
    // 业务类型 - 待分配具体值 todo
    private static final String SOURCE_BIZ_TYPE = "REIMBURSE";
    // 加密密钥 - 待分配具体值 todo
    private static final String PASSWORD = "payment_secret_key";
    // 重试次数
    private static final int MAX_RETRY_TIMES = 2;
    // 重试间隔（毫秒）
    private static final long RETRY_INTERVAL_MS = 1000;

    @Resource
    private Iface paymentService;
    @Resource
    private CnapsCodeQueryServiceRpc.Iface cnapsCodeQueryService;
    @Resource
    private FormPaymentDetailMapper formPaymentDetailMapper;

    // 摘要算法实现
    public static String generateAbstract(String data, String pwd) {
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
            md5.update(pwd.getBytes());
        } catch (NoSuchAlgorithmException e) {
            log.error("获取md5消息加密对象异常");
            throw new BusinessException("获取md5消息加密对象异常");
        }
        return Hex.encodeHexString(md5.digest(data.getBytes()));
    }

    /**
     * 生成数字摘要
     */
    private static String buildUnifyPaymentDigest(UnifyPaymentApplyRequest request) {
        StringBuilder sb = new StringBuilder();
        sb.append(StringUtils.trimToEmpty(request.getSourceType()));
        sb.append(StringUtils.trimToEmpty(request.getSourceNo()));
        sb.append(StringUtils.trimToEmpty(request.getSourceBizType()));
        sb.append(StringUtils.trimToEmpty(request.getPaymentSubject()));
        if (CollectionUtils.isNotEmpty(request.getPaymentChannelList())) {
            for (String channel : request.getPaymentChannelList()) {
                sb.append(StringUtils.trimToEmpty(channel));
            }
        }
        sb.append(StringUtils.trimToEmpty(request.getPaymentAccountNumber()));
        sb.append(StringUtils.trimToEmpty(request.getPaymentName()));
        sb.append(StringUtils.trimToEmpty(request.getPaymentAmount()));
        sb.append(StringUtils.trimToEmpty(request.getBeneficiaryChannelName()));
        sb.append(StringUtils.trimToEmpty(request.getBeneficiaryAccountNumber()));
        sb.append(StringUtils.trimToEmpty(request.getBeneficiaryAccountType()));
        sb.append(StringUtils.trimToEmpty(request.getBeneficiaryName()));
        sb.append(StringUtils.trimToEmpty(request.getPaymentDetails()));
        sb.append(StringUtils.trimToEmpty(request.getVersion()));
        if (request.getExtendRequest() != null) {
            sb.append(StringUtils.trimToEmpty(request.getExtendRequest().getBeneficiaryCellphone()));
            sb.append(StringUtils.trimToEmpty(request.getExtendRequest().getBeneficiaryCertNo()));
            sb.append(StringUtils.trimToEmpty(request.getExtendRequest().getBeneficiaryCertType()));
            sb.append(StringUtils.trimToEmpty(request.getExtendRequest().getBeneficiaryNationality()));
            sb.append(StringUtils.trimToEmpty(request.getExtendRequest().getSellerId()));
            sb.append(StringUtils.trimToEmpty(request.getExtendRequest().getSubAccountNo()));
            sb.append(StringUtils.trimToEmpty(request.getExtendRequest().getSubAccountName()));
        }
        if (request.getBankRequest() != null) {
            sb.append(StringUtils.trimToEmpty(request.getBankRequest().getBeneficiaryBankCode()));
            sb.append(StringUtils.trimToEmpty(request.getBankRequest().getBeneficiaryBankCodeType()));
            sb.append(StringUtils.trimToEmpty(request.getBankRequest().getPublicOrPrivate()));
            sb.append(StringUtils.trimToEmpty(request.getBankRequest().getChargesIndicator()));
            sb.append(StringUtils.trimToEmpty(request.getBankRequest().getBeneficiaryContactName()));
            sb.append(StringUtils.trimToEmpty(request.getBankRequest().getBeneficiaryContactPhone()));
            sb.append(StringUtils.trimToEmpty(request.getBankRequest().getBeneficiaryEmailAddress()));
        }
        return generateAbstract(sb.toString(), PASSWORD);
    }

    /**
     * 报销单据提交后需要进行付款校验，判断该单据是否可以进行打款
     *
     * @param paymentDto 付款DTO
     * @return 校验结果，校验不通过时返回失败原因
     */
    public ReimbursementFormCheckResultDto checkPaymentValidation(ReimbursePaymentDto paymentDto) {
        try {
            log.info("开始进行打款校验，校验参数：{}", JSON.toJSONString(paymentDto));
            // 1. 参数验证
            validateRequest(paymentDto);
            // 2. 构造校验请求
            UnifyPaymentApplyRequest request = buildPaymentRequest(paymentDto);
            // 3. 调用财务付款平台校验接口（带重试机制）
            log.info("调用财务付款平台校验接口，请求参数：{}", JSON.toJSONString(request));
            UnifyPaymentApplyResponse response = checkUnifyPaymentWithRetry(request);
            log.info("调用财务付款平台校验接口，响应结果：{}", JSON.toJSONString(response));
            // 4. 处理响应结果
            return processResponse(response, paymentDto.getFormNum());
        } catch (Exception e) {
            log.error("打款校验系统异常，单据号：{}，错误：{}", paymentDto.getFormNum(), e.getMessage(), e);
            return ReimbursementFormCheckResultDto.builder().checkPass(false).build();
        }
    }

    /**
     * 执行实际打款操作
     *
     * @param paymentDto 付款DTO
     * @return 打款结果，包含打款状态和相关信息
     */
    public Pair<String, UnifyPaymentApplyResponse> executePayment(ReimbursePaymentDto paymentDto) {
        String sourceNo = "";
        try {
            log.info("开始执行打款操作，打款参数：{}", JSON.toJSONString(paymentDto));
            // 1. 参数验证
            validateRequest(paymentDto);
            // 2. 构造打款请求
            UnifyPaymentApplyRequest request = buildPaymentRequest(paymentDto);
            sourceNo = request.getSourceNo();
            // 3. 调用财务付款平台打款接口（带重试机制）
            log.info("调用财务付款平台打款接口，请求参数：{}", JSON.toJSONString(request));
            UnifyPaymentApplyResponse response = executeUnifyPaymentWithRetry(request);
            log.info("调用财务付款平台打款接口，响应结果：{}", JSON.toJSONString(response));
            // 4. 处理响应结果
            return Pair.of(sourceNo, response);
        } catch (Exception e) {
            log.error("打款执行系统异常，单据号：{}，错误：{}", paymentDto.getFormNum(), e.getMessage(), e);
            return Pair.of(sourceNo, null);
        }
    }

    /**
     * 带重试机制的付款校验接口调用
     *
     * @param request 校验请求
     * @return 校验响应
     */
    private UnifyPaymentApplyResponse checkUnifyPaymentWithRetry(UnifyPaymentApplyRequest request) {
        return RetryUtil.executeWithRetry(
                () -> {
                    UnifyPaymentApplyResponse response;
                    try {
                        response = paymentService.checkUnifyPayment(new Context(), request);
                    } catch (TException e) {
                        throw new BusinessException("财务付款平台校验接口报错", e);
                    }
                    return response;
                },
                MAX_RETRY_TIMES,
                RETRY_INTERVAL_MS,
                "财务付款平台校验接口"
        );
    }

    /**
     * 带重试机制的打款接口调用
     *
     * @param request 打款请求
     * @return 打款响应
     */
    private UnifyPaymentApplyResponse executeUnifyPaymentWithRetry(UnifyPaymentApplyRequest request) {
        return RetryUtil.executeWithRetry(
                () -> {
                    UnifyPaymentApplyResponse response;
                    try {
                        response = paymentService.unifyPayment(new Context(), request);
                    } catch (TException e) {
                        throw new BusinessException("财务付款平台打款接口报错", e);
                    }
                    return response;
                },
                MAX_RETRY_TIMES,
                RETRY_INTERVAL_MS,
                "财务付款平台打款接口"
        );
    }

    /**
     * 参数验证
     */
    private void validateRequest(ReimbursePaymentDto paymentDto) {
        AssertHelper.notNull(paymentDto, "付款DTO不能为空");
        AssertHelper.check(paymentDto.getAmount().compareTo(BigDecimal.ZERO) > 0, "报销金额必须大于0");
        // 验证付款信息
        AssertHelper.notBlank(paymentDto.getSubjectCode(), "付款主体编码不能为空");
        // 验证境外付款限制
        AssertHelper.check(Objects.equals(paymentDto.getPaymentType(), 0), "暂不支持境外付款");
    }

    /**
     * 构造打款校验请求
     */
    private UnifyPaymentApplyRequest buildPaymentRequest(ReimbursePaymentDto paymentDto) {
        UnifyPaymentApplyRequest request = new UnifyPaymentApplyRequest();
        // 基础信息
        request.setSourceType(SOURCE_TYPE);
        request.setSourceNo(generateSourceNo(paymentDto.getFormNum()));
        request.setSourceBizType(SOURCE_BIZ_TYPE);
        request.setPaymentSubject(paymentDto.getSubjectCode());
        // 金额和币种
        request.setPaymentAmount(paymentDto.getAmount().toString());
        request.setPaymentCurrency(StringUtils.defaultIfBlank(paymentDto.getCurrency(), "CNY"));
        // 收款信息
        request.setBeneficiaryAccountNumber(paymentDto.getGatheringAccount());
        request.setBeneficiaryName(paymentDto.getGatheringName());
        request.setBeneficiaryChannelName(paymentDto.getGatheringBank());
        request.setPaymentDetails(paymentDto.getFormNum());
        // 银行信息
        UnifyPaymentApplyBankRequest bankRequest = new UnifyPaymentApplyBankRequest();
        // 调用联行号查询接口获取总行联行号
        String superBankCode = querySuperBankCode(paymentDto.getGatheringBank());
        bankRequest.setBeneficiaryBankCode(superBankCode);
        bankRequest.setPublicOrPrivate("PRIVATE");
        request.setBankRequest(bankRequest);
        // 生成数字摘要
        String digest = buildUnifyPaymentDigest(request);
        request.setDigitalDigest(digest);
        return request;
    }

    /**
     * 查询总行联行号
     *
     * @param bankName 分支联行号
     * @return 总行联行号
     */
    private String querySuperBankCode(String bankName) {
        if (StringUtils.isBlank(bankName)) {
            log.warn("分支联行号为空，无法查询总行联行号");
            throw new BusinessException("分支联行号为空，无法查询总行联行号");
        }
        try {
            CnapsCodeQueryRequest queryRequest = new CnapsCodeQueryRequest();
            queryRequest.setBankName(bankName);
            queryRequest.setSource(SOURCE_TYPE);
            log.info("查询联行号信息，请求参数：{}", JSON.toJSONString(queryRequest));
            CnapsCodeQueryResult queryResult = cnapsCodeQueryService.queryCnapsCodeInfoByCodeOrBankName(new Context(), queryRequest);
            log.info("查询联行号信息，响应结果：{}", JSON.toJSONString(queryResult));
            if (queryResult != null && queryResult.isSuccess() && CollectionUtils.isNotEmpty(queryResult.getCnapsInfos())) {
                String superBankCode = queryResult.getCnapsInfos().get(0).getSuperBankCode();
                if (StringUtils.isNotBlank(superBankCode)) {
                    log.info("成功获取总行联行号：{} -> {}", bankName, superBankCode);
                    return superBankCode;
                }
            }
        } catch (Exception e) {
            log.error("查询联行号异常，使用原联行号：{}，异常：{}", bankName, e.getMessage(), e);
        }
        throw new BusinessException("分支联行号为空，无法查询总行联行号");
    }

    /**
     * 生成业务单号（幂等键）
     */
    private String generateSourceNo(String formNum) {
        try {
            // 查询当前单据号的付款记录数，作为游标基础
            int currentCount = formPaymentDetailMapper.countByFormNo(formNum);
            int cursor = currentCount + 1;
            String sourceNo = formNum + "_" + cursor;
            log.info("生成业务单号，单据号：{}，当前记录数：{}，游标：{}，业务单号：{}", formNum, currentCount, cursor, sourceNo);
            return sourceNo;
        } catch (Exception e) {
            log.error("生成业务单号异常，单据号：{}，异常：{}", formNum, e.getMessage(), e);
            return formNum + "_999";
        }
    }

    /**
     * 处理校验响应结果
     */
    private ReimbursementFormCheckResultDto processResponse(UnifyPaymentApplyResponse response, String formNum) {
        log.info("打款校验响应，单据号：{}，响应：{}", formNum, JSON.toJSONString(response));
        AssertHelper.notNull(response, "打款校验响应为空");
        if (response.isSuccess()) {
            log.info("打款校验通过，单据号：{}", formNum);
            return ReimbursementFormCheckResultDto.builder().checkPass(true).build();
        } else {
            String failReason = StringUtils.defaultIfBlank(response.getFailReason(), "打款校验失败");
            log.warn("打款校验失败，单据号：{}，失败原因：{}", formNum, failReason);
            return ReimbursementFormCheckResultDto.builder().checkPass(false).build(); // todo 添加打款失败原因 如何与当前校验相结合
        }
    }

    /**
     * 处理打款响应结果
     */
    private ReimbursementFormCheckResultDto processPaymentResponse(UnifyPaymentApplyResponse response, String formNum) {
        log.info("打款执行响应，单据号：{}，响应：{}", formNum, JSON.toJSONString(response));
        AssertHelper.notNull(response, "打款执行响应为空");
        if (response.isSuccess()) {
            log.info("打款执行成功，单据号：{}", formNum);
            return ReimbursementFormCheckResultDto.builder().checkPass(true).build();
        } else {
            String failReason = StringUtils.defaultIfBlank(response.getFailReason(), "打款执行失败");
            log.warn("打款执行失败，单据号：{}，失败原因：{}", formNum, failReason);
            return ReimbursementFormCheckResultDto.builder().checkPass(false).build(); // todo 添加打款失败原因 如何与当前校验相结合
        }
    }
}
