package com.xhs.reimburse.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xhs.finance.sso.UserInfoBag;
import com.xhs.finance.sso.model.UserInfo;
import com.xhs.reimburse.modal.dto.RedFlowMsgDTO;
import com.xhs.reimburse.modal.dto.ReimbursePaymentDto;
import com.xhs.reimburse.service.external.finance.OAPaymentService;
import com.xhs.reimburse.service.external.redflow.RedFlowRpcService;
import com.xhs.reimburse.xhsoa.mapper.CommonFormMapper;
import com.xhs.reimburse.xhsoa.mapper.FormPaymentDetailMapper;
import com.xhs.reimburse.xhsoa.modal.CommonForm;
import com.xhs.reimburse.xhsoa.modal.FormPaymentDetail;
import com.xiaohongshu.fls.rpc.finance.cashiercenter.model.UnifyPaymentApplyResponse;
import com.xiaohongshu.fls.rpc.finance.workflow.task.req.OaRpcFallBackReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

import static com.xhs.reimburse.enums.ReimburseTypeEnum.CLBXD;
import static com.xhs.reimburse.enums.ReimburseTypeEnum.YBFYBXD;

// 流程与处理付款相关逻辑
@Slf4j
@Service
public class PaymentRedFlowService {
    public static final String FIRST_NODE = "Activity_submit"; // 发起人节点
    private static final String CASHIER_AUDIT_NODE = "cashier_audit"; // 出纳审批节点
    @Resource
    private OAPaymentService oaPaymentService;
    @Resource
    private FormPaymentDetailMapper formPaymentDetailMapper;
    @Resource
    private CommonFormMapper commonFormMapper;
    @Resource
    private RedFlowRpcService redFlowRpcService;

    // 流程节点变化引起的发起支付
    public void flowCausePayment(String formNum, RedFlowMsgDTO redFlowMsgDTO) {
        // todo 灰度逻辑还没加
        // 1. 只处理YBFYBXD、CLBXD类型
        if (!YBFYBXD.getType().equals(redFlowMsgDTO.getFormType()) && !CLBXD.getType().equals(redFlowMsgDTO.getFormType())) {
            log.info("非YBFYBXD、CLBXD类型，跳过处理，单据类型：{}, formNum:{}", redFlowMsgDTO.getFormType(), redFlowMsgDTO.getFormNo());
            return;
        }
        // 2. 判断当前在流程中，并且当前节点为出纳付款节点
        if (redFlowMsgDTO.getCurrentTaskInfo() == null || redFlowMsgDTO.getCurrentTaskInfo().isEmpty()) {
            log.info("当前不在流程中，跳过处理， formNum:{}", redFlowMsgDTO.getFormNo());
            return;
        }
        String currProcessNode = redFlowMsgDTO.getCurrentTaskInfo().get(0).getTaskNodeKey();
        if (!CASHIER_AUDIT_NODE.equals(currProcessNode)) {
            log.info("当前节点非出纳付款节点，跳过处理， formNum:{}", redFlowMsgDTO.getFormNo());
            return;
        }
        updateCommonForm(formNum, redFlowMsgDTO.getPaymentType(), redFlowMsgDTO.getAmount(), "CASHIER_AUDIT");
        // 4. 发起支付请求
        PaymentResult result = getPaymentResult(formNum, redFlowMsgDTO);
        // 5. 写入form_payment_detail表
        upsertFormPaymentDetail(formNum, redFlowMsgDTO, result.paymentNo, result.paySuccess, result.failReason);
        // 6. 根据支付结果更新common_form表
        String finalPayStatus = result.paySuccess ? "SUCCESS" : "FAIL";
        updateCommonForm(formNum, null, null, finalPayStatus);
        if (!result.paySuccess) {
            flowRefuse(formNum, result.failReason);
        }
    }

    // 撤回流程到发起人
    private void flowRefuse(String formNum, String failReason) {
        // 支付失败则需要驳回单据到发起人节点
        OaRpcFallBackReq req = new OaRpcFallBackReq();
        // 获取当前用户ID
        UserInfo userInfo = UserInfoBag.get();
        if (userInfo == null) {
            log.error("获取当前用户信息失败，无法执行驳回操作");
            return;
        }
        // 设置必填字段
        req.setUserId(userInfo.getUserId());
        req.setTargetNodeKey(FIRST_NODE); // 驳回到发起人提交节点
        req.setFormNum(formNum); // 单据号
        req.setIsSkipHistory("0"); // 不跳过历史节点，逐级审批
        req.setOpinion(failReason); // 审批意见
        log.info("执行驳回操作，formNum: {}, userId: {}, targetNodeKey: {}", req.getFormNum(), req.getUserId(), req.getTargetNodeKey());
        redFlowRpcService.fallBackNode(req);
    }

    // 更新common_form表
    private void updateCommonForm(String formNum, Integer paymentType, BigDecimal amount, String payStatus) {
        // 构造更新条件
        CommonForm updateForm = new CommonForm();
        updateForm.setFormNum(formNum);
        // 根据传入参数更新对应字段
        if (paymentType != null) {
            updateForm.setPaymentType(paymentType);
        }
        if (amount != null) {
            updateForm.setAmount(amount);
        }
        if (payStatus != null) {
            updateForm.setPayStatus(payStatus);
        }
        // 设置更新时间
        updateForm.setUpdateTime(new Date());
        // 根据 formNum 更新记录
        QueryWrapper<CommonForm> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("form_num", formNum);
        int updateResult = commonFormMapper.update(updateForm, queryWrapper);
        log.info("更新 common_form 表成功，formNum: {}, 更新结果: {}", formNum, updateResult);
    }

    // 发起支付请求
    private PaymentResult getPaymentResult(String formNum, RedFlowMsgDTO redFlowMsgDTO) {
        boolean paySuccess = false;
        String paymentNo = null;
        String failReason = null;
        try {
            ReimbursePaymentDto paymentDto = ReimbursePaymentDto.getReimbursePaymentDto(formNum, redFlowMsgDTO);
            Pair<String, UnifyPaymentApplyResponse> paymentRespPair = oaPaymentService.executePayment(paymentDto);
            UnifyPaymentApplyResponse paymentResp = paymentRespPair.getRight();
            if (paymentResp != null) {
                paySuccess = paymentResp.success;
                failReason = paymentResp.failReason;
            }
            paymentNo = paymentRespPair.getLeft();
        } catch (Exception e) {
            log.error("发起支付请求失败", e);
        }
        return new PaymentResult(paySuccess, paymentNo, failReason);
    }

    // 更新或者插入支付流水表
    private void upsertFormPaymentDetail(String formNum, RedFlowMsgDTO redFlowMsgDTO, String paymentNo, boolean paySuccess, String failReason) {
        // 根据formNum, paymentNo查询有效状态数据，如果存在则更新，如果不存在则插入
        FormPaymentDetail existingDetail = formPaymentDetailMapper.selectByFormNoAndPaymentNo(formNum, paymentNo);
        if (existingDetail != null) {
            // 如果存在则更新
            FormPaymentDetail updateDetail = new FormPaymentDetail();
            buildFormPaymentDetail(redFlowMsgDTO, paySuccess, updateDetail);
            updateDetail.setReason(failReason);
            updateDetail.setUpdateTime(new Date());
            UpdateWrapper<FormPaymentDetail> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("form_no", formNum).eq("payment_no", paymentNo).eq("is_valid", 1);
            int updateResult = formPaymentDetailMapper.update(updateDetail, updateWrapper);
            log.info("更新 form_payment_detail 表成功，formNum: {}, paymentNo: {}, 更新结果: {}", formNum, paymentNo, updateResult);
        } else {
            // 如果不存在则插入
            FormPaymentDetail detail = new FormPaymentDetail();
            detail.setPaymentNo(paymentNo);
            detail.setFormNo(formNum);
            buildFormPaymentDetail(redFlowMsgDTO, paySuccess, detail);
            detail.setIsValid(1);
            detail.setRemark("");
            detail.setReason(failReason);
            detail.setCreateTime(new Date());
            detail.setUpdateTime(new Date());
            int insertResult = formPaymentDetailMapper.insert(detail);
            log.info("插入 form_payment_detail 表成功，formNum: {}, paymentNo: {}, 插入结果: {}", formNum, paymentNo, insertResult);
        }
    }

    private void buildFormPaymentDetail(RedFlowMsgDTO redFlowMsgDTO, boolean paySuccess, FormPaymentDetail detail) {
        detail.setPaymentStatus(paySuccess ? "SUCCESS" : "FAIL");
        detail.setAmount(redFlowMsgDTO.getAmount());
        detail.setGatheringName(redFlowMsgDTO.getPayee());
        detail.setSubject(redFlowMsgDTO.getPaymentCompanyName());
        detail.setGatheringAccount(redFlowMsgDTO.getRecipientAccount());
        detail.setBankName(redFlowMsgDTO.getRecipientBank());
        detail.setPaymentCurrency(redFlowMsgDTO.getCurrency());
    }

    private static class PaymentResult {
        public final boolean paySuccess;
        public final String paymentNo;
        public final String failReason;

        public PaymentResult(boolean paySuccess, String paymentNo, String failReason) {
            this.paySuccess = paySuccess;
            this.paymentNo = paymentNo;
            this.failReason = failReason;
        }
    }
}
