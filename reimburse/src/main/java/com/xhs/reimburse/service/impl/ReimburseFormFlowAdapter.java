package com.xhs.reimburse.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.reimburse.assembler.PaymentAssembler;
import com.xhs.reimburse.assembler.ReimbursementFormAssembler;
import com.xhs.reimburse.constant.ApiHubConstant;
import com.xhs.reimburse.enums.ExpenseFirstSubjectEnum;
import com.xhs.reimburse.enums.ExpenseFormTypeEnum;
import com.xhs.reimburse.enums.ReimbursementFormStatusEnum;
import com.xhs.reimburse.modal.dto.*;
import com.xhs.reimburse.modal.dto.travel.ApiHubTravelDto;
import com.xhs.reimburse.modal.dto.travel.TravelApplyFormInfoDto;
import com.xhs.reimburse.modal.entity.EhrDepartmentEntity;
import com.xhs.reimburse.modal.entity.EmployeeEntity;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.modal.request.ApiHubRequest;
import com.xhs.reimburse.modal.request.RedFlowProcessSaveOrStartRequest;
import com.xhs.reimburse.modal.request.ReimburseFormRequest;
import com.xhs.reimburse.modal.response.ReimbursementFormPrintResponse;
import com.xhs.reimburse.modal.response.ReimbursementFormResponse;
import com.xhs.reimburse.rpc.consumer.MultiCdnRpcService;
import com.xhs.reimburse.service.*;
import com.xhs.reimburse.service.external.apihub.ApiHubRpcService;
import com.xhs.reimburse.service.external.ehr.EhrDepartmentRpcService;
import com.xhs.reimburse.service.external.ehr.EhrEmployeeRpcService;
import com.xhs.reimburse.service.external.finance.PaymentRpcService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
public abstract class ReimburseFormFlowAdapter implements ReimburseFormFlowComponentService {

    @Resource
    private ApiHubRpcService apiHubRpcService;

    @Resource
    private ReimbursementFormService reimbursementFormService;

    @Resource
    private PaymentAssembler paymentAssembler;

    @Resource
    private RelationReimbursementFormExpenseService formExpenseRelService;

    @Resource
    private PaymentRpcService paymentRpcService;

    @Resource
    private ExpenseService expenseService;

    @Resource
    private MultiCdnRpcService multiCdnRpcService;

    @Resource
    private ReimbursementFormAssembler formAssembler;

    @Resource
    private EhrDepartmentRpcService ehrDepartmentRpcService;

    @Resource
    private EhrEmployeeRpcService ehrEmployeeRpcService;

    @Resource
    private TravelApplyService travelApplyService;

    @Resource
    private RelationExpenseInvoiceService expenseInvoiceService;

    @Resource
    protected PaymentRedFlowService paymentRedFlowService;

    @ApolloJsonValue("${reimburse_water_line_amount:500.00}")
    private String waterLineAmount;

    @ApolloJsonValue("${reimburse_first_line_amount:10000.00}")
    private String bizFirstLineAmount;

    @ApolloJsonValue("${reimburse_second_line_amount:50000.00}")
    private String bizSecondLineAmount;

    @ApolloJsonValue("${inside_control_users:[]}")
    private List<String> insideControlUsers;

    /**
     * <br>2.2.1 预算部门是否存在 <br/>
     * <br>2.2.2 财务爱马仕系统付款信息基础校验 <br/>
     */
    @Override
    public void checkFormCommonData(ReimburseFormRequest formRequest) {

        ReimburseBasicInfo basicInfo = formRequest.getReimburseBasicInfoVo();
        PaymentInfoDto paymentInfo = formRequest.getPaymentInfo();

        //1 预算部门是否存在
        Long budgetDepartmentId = Long.parseLong(basicInfo.getBudgetDepartmentId());
        EhrDepartmentEntity ehrDepartmentEntity
                = ehrDepartmentRpcService.queryDepartmentByDepartmentId(budgetDepartmentId, false);
        AssertHelper.check(ehrDepartmentEntity.getIsValid() == 1, "预算部门不存在，请重新选择预算部门");

        //2 财务爱马仕系统付款信息基础校验
        if (Objects.nonNull(paymentInfo) && BooleanUtils.isTrue(paymentInfo.getCanBankEnterprise())) {
            String resultDesc = paymentRpcService.batchCheckPayments(paymentAssembler.toCheckDto(formRequest, paymentInfo));
            AssertHelper.isBlank(resultDesc, resultDesc);
        }

        List<String> expenseNos = formRequest.getExpenseNos();
        List<ExpenseDto> expenses = formRequest.getExpenses();
        AssertHelper.check(CollUtil.isNotEmpty(expenseNos) && CollUtil.isNotEmpty(expenses), "未查询到费用");

        //3 费用完整性校验
        for (ExpenseDto expense : expenses) {

            int expenseIndex = expenseNos.indexOf(expense.getUuid()) + 1;

            //费用状态阻碍校验
            String expenseBlockSubmitAlertMsg = expense.expenseBlockSubmitAlert();
            AssertHelper.isBlank(expenseBlockSubmitAlertMsg, String.format(expenseBlockSubmitAlertMsg, expenseIndex));

            //4 发票验真和发票购方名称应与付款公司是否一致
            if (CollectionUtils.isNotEmpty(expense.getRelationInvoiceList())) {

                //不需要校验发票主体的科目: 一般费用报销的【其他科目】不进行公司主体校验
                boolean nonCheckCompanySubject = ExpenseFormTypeEnum.YBFY.getFormTypeCode().equals(expense.getFormType())
                        && ExpenseFirstSubjectEnum.YBFY_OTHER.getSubjectCode().equals(expense.getFirstSubject());
                //不需要校验时付款公司传空即可
                String paymentCompanyName = nonCheckCompanySubject ? "" : basicInfo.getPaymentCompanyName();

                expense.getRelationInvoiceList().forEach(invoice -> {
                    String invoiceBlockSubmitAlertMsg = invoice.invoiceBlockSubmitAlert(paymentCompanyName);
                    AssertHelper.isBlank(invoiceBlockSubmitAlertMsg, String.format(invoiceBlockSubmitAlertMsg, expenseIndex));
                });
            }
        }
    }

    @Override
    public RedFlowProcessSaveOrStartRequest buildVariableMap(ReimburseFormRequest formRequest, boolean save) {
        Map<String, Object> variableMap = new HashMap<>();
        variableMap.put("formType", formRequest.getFormType());
        if (!save) {
            BigDecimal amount = formRequest.getAmount();
            EmployeeEntity creator = formRequest.getCreatorInfo();
            variableMap.put("userLevel", insideControlUsers.contains(creator.getUserId()) ? "4" : "5");
            variableMap.put("expenseAmount", amount);
            variableMap.put("waterlineExceed", amount.compareTo(new BigDecimal(waterLineAmount)) > 0 ? "true" : "false");
            variableMap.put("userType", creator.getEmployeeType() == 1 ? "1" : "2");
            //是否需要交单（包含纸质发票）
            variableMap.put("need_submit_form", BooleanUtils.isTrue(formRequest.getNeedPrint()) ? "true" : "false");

            //填充财务、付款相关字段
            setPaymentVariable(variableMap, formRequest);

            Long budgetDepartmentId = null;
            ReimburseBasicInfo reimburseBasicInfoVo = formRequest.getReimburseBasicInfoVo();
            if (Objects.nonNull(reimburseBasicInfoVo) && StrUtil.isNotBlank(reimburseBasicInfoVo.getBudgetDepartmentId())) {
                budgetDepartmentId = Long.parseLong(reimburseBasicInfoVo.getBudgetDepartmentId());
            }

            ReimbursementAuditBodyDto auditBody = new ReimbursementAuditBodyDto(formRequest.getFormType(), amount.toString(), budgetDepartmentId);
            if (creator.getEmployeeType() != 1) {
                //填充业务审核
                setBusinessAuditor(variableMap, auditBody);
            }

            //财务一、二分析审核
            setFianceBpAuditor(variableMap, auditBody);
        }

        RedFlowProcessSaveOrStartRequest request = new RedFlowProcessSaveOrStartRequest();
        request.setFormNum(formRequest.getFormNum());
        request.setFormType(formRequest.getFormType());
        request.setUuid(StrUtil.isBlank(formRequest.getUuid()) ? formRequest.generatedUuid() : formRequest.getUuid());
        request.setStartUserId(formRequest.getCreatorNo());
        request.setVariableMap(variableMap);
        return request;
    }

    @Override
    public ReimbursementFormResponse buildReimbursementForm(ReimbursementFormEntity reimburseForm, ReimburseFormRequest formRequest) {
        return formAssembler.toQueryResp(reimburseForm, formRequest);
    }

    @Override
    public ReimbursementFormPrintResponse buildFormPrintResponse(ReimbursementFormEntity reimbursementForm) {
        return formAssembler.toPrintResp(reimbursementForm);
    }

    /**
     * 单据草稿提交
     * <br> 1 报销单状态: 提交 -> 审核中，保存 -> 数据库已经存在数据则按照已有状态(自动保存时不会进行覆盖)
     * <br> 2 发票状态 -> 审核中
     * <br> 3 费用状态 -> 审核中
     *
     * @param formNum 单据号
     */
    @Override
    public void formSubmit(String formNum, boolean save) {
        ReimbursementFormStatusEnum statusEnum = null;
        if (!save) {
            statusEnum = ReimbursementFormStatusEnum.SHZ;
        } else {
            ReimbursementFormEntity form = reimbursementFormService.getReimbursementFormByFormNum(formNum, false);
            statusEnum = Objects.nonNull(form)
                    ? ReimbursementFormStatusEnum.getEnumByStatus(form.getReimburseStatus())
                    : ReimbursementFormStatusEnum.CG;
        }
        reimbursementFormService.updateReimbursementFormAllStatus(formNum, statusEnum);
    }

    /**
     * 审批人「驳回」至「发起人提交」节点
     * 发票状态 -> 审核中
     * 费用状态 -> 审核中
     * 报销单状态 -> 驳回
     *
     * @param formNum 单据号
     */
    @Override
    public void formRefuse(String formNum) {
        reimbursementFormService.updateReimbursementFormAllStatus(formNum, ReimbursementFormStatusEnum.BH);
    }

    /**
     * 发起人「撤回」单据
     * 发票状态 -> 审核中
     * 费用状态 -> 审核中
     * 报销单状态 -> 撤回
     *
     * @param formNum 单据号
     */
    @Override
    public void formWithdrawal(String formNum) {
        reimbursementFormService.updateReimbursementFormAllStatus(formNum, ReimbursementFormStatusEnum.CH);
    }

    /**
     * 发起人「中止」单据
     * （1）修改三个状态
     * （2）删除报销单&费用关系
     * 发票状态 -> 可提报
     * 费用状态 -> 可提报
     * 报销单状态 -> 已中止
     *
     * @param formNum 单据号
     */
    @Override
    public void formTerminate(String formNum) {
        reimbursementFormService.updateReimbursementFormAllStatus(formNum, ReimbursementFormStatusEnum.YZZ);
    }

    /**
     * 发起人「删除」单据
     * （1）修改三个状态
     * （2）删除报销单&费用关系
     * 发票状态 -> 可提报
     * 费用状态 -> 可提报
     * 报销单状态 -> 已删除
     *
     * @param formNum 单据号
     */
    @Override
    public void formDelete(String formNum) {
        reimbursementFormService.updateReimbursementFormAllStatus(formNum, ReimbursementFormStatusEnum.YSC);
    }

    /**
     * 单据审批完成
     * 发票状态 -> 已报销
     * 费用状态 -> 已报销
     * 报销单状态 -> 已报销
     *
     * @param formNum 单据号
     */
    @Override
    public void formComplete(String formNum) {
        reimbursementFormService.updateReimbursementFormAllStatus(formNum, ReimbursementFormStatusEnum.YWJ);
    }

    @Override
    public void submitAfterAction(ReimbursementFormEntity reimburseForm, ReimburseFormRequest formRequest, boolean save) {
        //新增或者更新单据信息
        reimbursementFormService.addOrUpdateReimbursementForm4Submit(reimburseForm);

        // 重建费用与单据的关联关系
        formExpenseRelService.updateReimbursementFormExpenseRelation(reimburseForm.getCreatorNo(), reimburseForm.getUuid(), formRequest.getExpenseNos());

        // 提交成功更新状态
        formSubmit(reimburseForm.getFormNum(), save);
    }

    @Override
    public void fillBaseInfo(ReimburseFormRequest formRequest, boolean save) {

        //校验的填充基础信息
        fillCheckedInfo(formRequest, save);

        //报销单附件预解析
        List<FileInfoDto> fileAttachments = formRequest.getFileAttachmentList();
        if (CollectionUtils.isNotEmpty(fileAttachments)) {
            multiCdnRpcService.batchGetAndSetFileUrl(fileAttachments);
            formRequest.setFileAttachmentList(fileAttachments);
        }

    }

    /**
     * 校验时填充基础信息
     */
    @Override
    public void fillCheckedInfo(ReimburseFormRequest formRequest, boolean save) {

        //填充提交人信息
        EmployeeEntity creatorInfo = ehrEmployeeRpcService.queryEhrEmployeeEntity(formRequest.getCreatorNo(), true);
        formRequest.setCreatorInfo(creatorInfo);

        //报销基础信息
        ReimburseBasicInfo reimburseBasicInfoVo = formRequest.getReimburseBasicInfoVo();
        if (save && Objects.isNull(reimburseBasicInfoVo)) {
            reimburseBasicInfoVo = formAssembler.generateReimburseBasicInfo(creatorInfo);
            formRequest.setReimburseBasicInfoVo(reimburseBasicInfoVo);
        }

        //获取费用
        List<ExpenseDto> expenses = this.getFillExpenses(formRequest);
        //填充费用
        formRequest.setExpenses(expenses);
        //计算单据总金额
        formRequest.setAmount(expenseService.expenseSumAmount(expenses));
        //是否展示需要打印: 需要交票并且不是草稿状态
        boolean isDraft = true;
        if (StrUtil.isNotBlank(formRequest.getFormNum())) {
            ReimbursementFormEntity reimbursementForm
                    = reimbursementFormService.getReimbursementFormByFormNum(formRequest.getFormNum(), false);
            isDraft = ReimbursementFormStatusEnum.CG.getCode().equals(reimbursementForm.getReimburseStatus()) && save;
        }
        boolean needPrint = expenseInvoiceService.confirmNeedSubmitInvoice(expenses) && !isDraft;
        formRequest.setNeedPrint(needPrint);
    }

    /**
     * 获取表单校验状态
     *
     * @param request 请求信息
     * @return 是否可提报 true 可提报
     */
    @Override
    public boolean getFormCheckedStatus(ReimburseFormRequest request) {
        List<ExpenseDto> expenses = request.getExpenses();
        ReimburseBasicInfo reimburseBasicInfoVo = request.getReimburseBasicInfoVo();
        //基础信息字段是否全部必填
        boolean baseCheck = Objects.nonNull(reimburseBasicInfoVo) && reimburseBasicInfoVo.fieldsCheck4Save();

        //是否填充费用
        if (CollectionUtils.isEmpty(expenses) || !baseCheck) {
            return false;
        }

        for (ExpenseDto expense : expenses) {

            //费用是否阻碍提交
            if (StrUtil.isNotBlank(expense.expenseBlockSubmitAlert())) {
                return false;
            }

            //发票阻碍提交
            if (CollectionUtils.isNotEmpty(expense.getRelationInvoiceList())) {
                //验真失败 || 报销单选择的付款公司与发票购方主体不一致
                if (expense.getRelationInvoiceList().stream().anyMatch(invoice ->
                        StrUtil.isNotBlank(invoice.invoiceBlockSubmitAlert(reimburseBasicInfoVo.getPaymentCompanyName())))) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 填充入账、付款相关字段
     */
    private void setPaymentVariable(Map<String, Object> variableMap, ReimburseFormRequest formRequest) {
        PaymentInfoDto paymentInfo = formRequest.getPaymentInfo();
        ReimburseBasicInfo reimburseBasicInfoVo = formRequest.getReimburseBasicInfoVo();
        if (Objects.nonNull(paymentInfo) && BooleanUtils.isTrue(paymentInfo.getCanBankEnterprise())
                && Objects.nonNull(reimburseBasicInfoVo)) {
            variableMap.put("subject", paymentInfo.getSubjectCode());
            variableMap.put("paymentType", paymentInfo.getPaymentType());
            variableMap.put("paymentAbility", paymentInfo.getPaymentAbility());
            variableMap.put("amount", formRequest.getAmount());
            variableMap.put("currency", formRequest.getCurrency());
            variableMap.put("accountType", formRequest.getAccountType());
            variableMap.put("payee", reimburseBasicInfoVo.getGatheringName());
            variableMap.put("recipientAccount", reimburseBasicInfoVo.getGatheringAccount());
            variableMap.put("recipientBank", reimburseBasicInfoVo.getGatheringBank());
            variableMap.put("recipientBankCode", reimburseBasicInfoVo.getGatheringBankCode());
            variableMap.put("paymentCompanyName", reimburseBasicInfoVo.getPaymentCompanyName());
            variableMap.put("paymentWays", Collections.singletonList(formRequest.getPaymentType()));
            //财务入账需要字段 预算部门ID
            variableMap.put("budgetDepartmentId", reimburseBasicInfoVo.getBudgetDepartmentId());
        }
        //财务入账需要字段 费用UUID
        variableMap.put("eIds", formRequest.getExpenseNos());
    }

    /**
     * 业务审核字段
     */
    private void setBusinessAuditor(Map<String, Object> variableMap, ReimbursementAuditBodyDto auditBody) {
        ApiHubRequest apiHubRequest = ApiHubRequest.builder().apiCode(ApiHubConstant.QUERY_EXPENSE_BIZ_AUDITORS)
                .businessType(ApiHubConstant.OA_BUSINESS_TYPE).businessSubType(ApiHubConstant.OA_BUSINESS_SUB_TYPE)
                .businessElement(ApiHubConstant.OA_ELEMENT_BUSINESS_AUDIT).content(JSON.toJSONString(auditBody))
                .build();
        JSONObject resp = apiHubRpcService.callApiGetObject(apiHubRequest);
        if (Objects.isNull(resp)) {
            return;
        }
        ApiHubBusinessAuditRespDto businessAudit = resp.toJavaObject(ApiHubBusinessAuditRespDto.class);
        List<String> data = businessAudit.getData();
        if (BooleanUtils.isTrue(businessAudit.isSuccess()) && CollectionUtils.isNotEmpty(data)) {
            for (int i = 1; i <= 5; i++) {
                if (i <= data.size()) {
                    variableMap.put("businessAudit_" + i, data.get(i - 1));
                }
            }
        }
    }

    /**
     * 获取财务分析审核
     */
    private void setFianceBpAuditor(Map<String, Object> variableMap, ReimbursementAuditBodyDto auditBody) {
        ApiHubRequest apiHubRequest = ApiHubRequest.builder().apiCode(ApiHubConstant.QUERY_EXPENSE_FINANCE_BP)
                .businessType(ApiHubConstant.OA_BUSINESS_TYPE).businessSubType(ApiHubConstant.OA_BUSINESS_SUB_TYPE)
                .businessElement(ApiHubConstant.OA_ELEMENT_BUSINESS_AUDIT).content(JSON.toJSONString(auditBody))
                .build();
        JSONObject resp = apiHubRpcService.callApiGetObject(apiHubRequest);
        if (Objects.isNull(resp)) {
            return;
        }
        ApiHubTravelDto apiHubTravelDto = resp.toJavaObject(ApiHubTravelDto.class);
        Map<String, List<String>> data = apiHubTravelDto.getData();
        if (BooleanUtils.isTrue(apiHubTravelDto.isSuccess()) && MapUtils.isNotEmpty(data)) {
            for (Map.Entry<String, List<String>> entry : data.entrySet()) {
                variableMap.put("need" + entry.getKey(), CollectionUtils.isNotEmpty(entry.getValue()) ? "1" : "");
            }
            variableMap.putAll(data);
        }
    }

    /**
     * 获取预填充费用
     *
     * @param formRequest 单据表单
     * @return 费用
     */
    private List<ExpenseDto> getFillExpenses(ReimburseFormRequest formRequest) {

        String paymentCompanyName = "";
        ReimburseBasicInfo reimburseBasicInfoVo = formRequest.getReimburseBasicInfoVo();
        if (Objects.nonNull(reimburseBasicInfoVo)) {
            paymentCompanyName = reimburseBasicInfoVo.getPaymentCompanyName();
        }

        List<TravelApplyFormInfoDto> travelApplyFormInfos = null;
        if (CollUtil.isNotEmpty(formRequest.getTravelApplyFormNums())) {
            travelApplyFormInfos = travelApplyService.queryTravelApplyScheduleDetail(formRequest.getTravelApplyFormNums());
        }

        return expenseService.getExpenseCheckInfos(formRequest.getExpenseNos(), travelApplyFormInfos, paymentCompanyName);
    }
}