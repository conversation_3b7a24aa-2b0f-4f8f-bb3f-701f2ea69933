package com.xhs.reimburse.modal.dto;

import com.xhs.reimburse.modal.request.ReimburseFormRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ReimbursePaymentDto {
    // 付款金额
    private BigDecimal amount;
    // 付款主体编码
    private String subjectCode;
    //收款账户属性 0境内 1境外
    private Integer paymentType = 0;
    // 单据号
    private String formNum;
    //默认币种 CNY
    private String currency = "CNY";
    // 收款账号
    private String gatheringAccount;
    // 收款人名称
    private String gatheringName;
    // 收款银行名称
    private String gatheringBank;


    /**
     * 将ReimburseFormRequest转换为ReimbursePaymentDto
     *
     * @param formRequest 报销单据请求
     * @return 付款DTO
     */
    public static ReimbursePaymentDto convertToPaymentDto(ReimburseFormRequest formRequest) {
        if (formRequest == null) {
            return null;
        }
        ReimbursePaymentDto paymentDto = new ReimbursePaymentDto();
        paymentDto.setAmount(formRequest.getAmount());
        paymentDto.setFormNum(formRequest.getFormNum());
        paymentDto.setPaymentType(formRequest.getPaymentType());
        paymentDto.setCurrency(formRequest.getCurrency());
        // 设置付款主体编码
        if (formRequest.getPaymentInfo() != null) {
            paymentDto.setSubjectCode(formRequest.getPaymentInfo().getSubjectCode());
        }
        // 设置收款信息
        if (formRequest.getReimburseBasicInfoVo() != null) {
            paymentDto.setGatheringAccount(formRequest.getReimburseBasicInfoVo().getGatheringAccount());
            paymentDto.setGatheringName(formRequest.getReimburseBasicInfoVo().getGatheringName());
            paymentDto.setGatheringBank(formRequest.getReimburseBasicInfoVo().getGatheringBank());
        }
        return paymentDto;
    }

    // 构造支付请求
    public static ReimbursePaymentDto getReimbursePaymentDto(String formNum, RedFlowMsgDTO redFlowMsgDTO) {
        ReimbursePaymentDto paymentDto = new ReimbursePaymentDto();
        paymentDto.setAmount(redFlowMsgDTO.getAmount());
        paymentDto.setFormNum(formNum);
        paymentDto.setPaymentType(redFlowMsgDTO.getPaymentType());
        paymentDto.setCurrency(redFlowMsgDTO.getCurrency());
        paymentDto.setGatheringAccount(redFlowMsgDTO.getRecipientAccount());
        paymentDto.setGatheringName(redFlowMsgDTO.getPayee());
        paymentDto.setGatheringBank(redFlowMsgDTO.getRecipientBank());
        paymentDto.setSubjectCode(redFlowMsgDTO.getPaymentCompanyName());
        return paymentDto;
    }

}
